# 使用 Ubuntu 20.04.2 LTS 作为基础镜像
FROM ubuntu:20.04

# 设置非交互模式，避免安装过程中的询问
ENV DEBIAN_FRONTEND=noninteractive

# 更新软件源列表并安装必需的软件包
RUN apt-get update && apt-get install -y \
    wget \
    ca-certificates  # 确保 SSL 连接没有问题

# 下载预编译的 FFmpeg
RUN wget https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz \
    && tar -xJf ffmpeg-release-amd64-static.tar.xz \
    && mv ffmpeg-*-amd64-static/ffmpeg /usr/bin/ \
    && mv ffmpeg-*-amd64-static/ffprobe /usr/bin/ \
    && rm -rf ffmpeg-*-amd64-static ffmpeg-release-amd64-static.tar.xz

# 清理缓存以减小镜像大小
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /data

# 默认命令
CMD ["ffmpeg", "-version"]

