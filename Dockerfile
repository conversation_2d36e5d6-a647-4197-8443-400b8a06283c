FROM python:3.10.10
# Build PlatForm OS is Ubuntu 20.04.2 Ubuntu 20.04.2 LTS (GNU/Linux 5.4.0-163-generic x86_64)
# Run updates and install ffmpeg
RUN apt update && apt install ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy and install the requirements
COPY ./requirements.txt /requirements.txt

# Pip install the dependencies
RUN pip install --upgrade pip 
RUN pip install --no-cache-dir -r /requirements.txt

# Copy the current directory contents into the container at /app
COPY main.py /app/main.py
COPY model/* /app/model/
# Set the working directory to /app
WORKDIR /app

# Expose port 8000
EXPOSE 8000

# Run the app
CMD uvicorn main:app --host 0.0.0.0
